using System.Net.Security;
using System.Security.Authentication;
using System.Security.Cryptography.X509Certificates;
using Liam.TcpClient.Constants;

namespace Liam.TcpClient.Models;

/// <summary>
/// TCP客户端配置
/// </summary>
public class TcpClientConfig
{
    /// <summary>
    /// 服务器主机名或IP地址
    /// </summary>
    public string Host { get; set; } = "localhost";

    /// <summary>
    /// 服务器端口号
    /// </summary>
    public int Port { get; set; } = TcpClientConstants.Defaults.Port;

    /// <summary>
    /// 连接超时时间（秒）
    /// </summary>
    public int ConnectionTimeoutSeconds { get; set; } = TcpClientConstants.Defaults.ConnectionTimeoutSeconds;

    /// <summary>
    /// 接收缓冲区大小
    /// </summary>
    public int ReceiveBufferSize { get; set; } = TcpClientConstants.Defaults.ReceiveBufferSize;

    /// <summary>
    /// 发送缓冲区大小
    /// </summary>
    public int SendBufferSize { get; set; } = TcpClientConstants.Defaults.SendBufferSize;

    /// <summary>
    /// 是否启用心跳检测
    /// </summary>
    public bool EnableHeartbeat { get; set; } = true;

    /// <summary>
    /// 心跳间隔（秒）
    /// </summary>
    public int HeartbeatIntervalSeconds { get; set; } = TcpClientConstants.Defaults.HeartbeatIntervalSeconds;

    /// <summary>
    /// 心跳超时时间（秒）
    /// </summary>
    public int HeartbeatTimeoutSeconds { get; set; } = TcpClientConstants.Defaults.HeartbeatTimeoutSeconds;

    /// <summary>
    /// 是否启用自动重连
    /// </summary>
    public bool EnableAutoReconnect { get; set; } = true;

    /// <summary>
    /// 重连间隔（秒）
    /// </summary>
    public int ReconnectIntervalSeconds { get; set; } = TcpClientConstants.Defaults.ReconnectIntervalSeconds;

    /// <summary>
    /// 最大重连次数（-1表示无限重连）
    /// </summary>
    public int MaxReconnectAttempts { get; set; } = TcpClientConstants.Defaults.MaxReconnectAttempts;

    /// <summary>
    /// 消息最大长度
    /// </summary>
    public int MaxMessageLength { get; set; } = TcpClientConstants.Defaults.MaxMessageLength;

    /// <summary>
    /// 是否启用SSL/TLS
    /// </summary>
    public bool EnableSsl { get; set; } = false;

    /// <summary>
    /// SSL配置
    /// </summary>
    public SslConfig? SslConfig { get; set; }

    /// <summary>
    /// 连接池配置
    /// </summary>
    public ConnectionPoolConfig? ConnectionPoolConfig { get; set; }

    /// <summary>
    /// 客户端标识符
    /// </summary>
    public string? ClientId { get; set; }

    /// <summary>
    /// 客户端名称
    /// </summary>
    public string? ClientName { get; set; }

    /// <summary>
    /// 是否启用统计信息收集
    /// </summary>
    public bool EnableStatistics { get; set; } = true;

    /// <summary>
    /// 统计信息更新间隔（秒）
    /// </summary>
    public int StatisticsUpdateIntervalSeconds { get; set; } = TcpClientConstants.Performance.StatisticsUpdateIntervalSeconds;

    /// <summary>
    /// 验证配置
    /// </summary>
    /// <returns>验证结果</returns>
    public ValidationResult Validate()
    {
        var errors = new List<string>();

        if (string.IsNullOrWhiteSpace(Host))
        {
            errors.Add("Host不能为空");
        }

        if (Port <= 0 || Port > 65535)
        {
            errors.Add("Port必须在1-65535范围内");
        }

        if (ConnectionTimeoutSeconds <= 0)
        {
            errors.Add("ConnectionTimeoutSeconds必须大于0");
        }

        if (ReceiveBufferSize <= 0)
        {
            errors.Add("ReceiveBufferSize必须大于0");
        }

        if (SendBufferSize <= 0)
        {
            errors.Add("SendBufferSize必须大于0");
        }

        if (EnableHeartbeat)
        {
            if (HeartbeatIntervalSeconds <= 0)
            {
                errors.Add("HeartbeatIntervalSeconds必须大于0");
            }

            if (HeartbeatTimeoutSeconds <= 0)
            {
                errors.Add("HeartbeatTimeoutSeconds必须大于0");
            }
        }

        if (EnableAutoReconnect)
        {
            if (ReconnectIntervalSeconds <= 0)
            {
                errors.Add("ReconnectIntervalSeconds必须大于0");
            }

            if (MaxReconnectAttempts < -1)
            {
                errors.Add("MaxReconnectAttempts必须大于等于-1");
            }
        }

        if (MaxMessageLength <= 0)
        {
            errors.Add("MaxMessageLength必须大于0");
        }

        if (EnableSsl && SslConfig != null)
        {
            var sslValidation = SslConfig.Validate();
            if (!sslValidation.IsValid)
            {
                errors.AddRange(sslValidation.Errors.Select(e => $"SSL配置错误: {e}"));
            }
        }

        if (ConnectionPoolConfig != null)
        {
            var poolValidation = ConnectionPoolConfig.Validate();
            if (!poolValidation.IsValid)
            {
                errors.AddRange(poolValidation.Errors.Select(e => $"连接池配置错误: {e}"));
            }
        }

        return new ValidationResult(errors.Count == 0, errors);
    }

    /// <summary>
    /// 创建默认配置
    /// </summary>
    /// <param name="host">服务器主机</param>
    /// <param name="port">服务器端口</param>
    /// <returns>默认配置</returns>
    public static TcpClientConfig CreateDefault(string host = "localhost", int port = TcpClientConstants.Defaults.Port)
    {
        return new TcpClientConfig
        {
            Host = host,
            Port = port
        };
    }

    /// <summary>
    /// 创建SSL配置
    /// </summary>
    /// <param name="host">服务器主机</param>
    /// <param name="port">服务器端口</param>
    /// <param name="serverName">SSL服务器名称</param>
    /// <returns>SSL配置</returns>
    public static TcpClientConfig CreateSslConfig(string host, int port, string? serverName = null)
    {
        return new TcpClientConfig
        {
            Host = host,
            Port = port,
            EnableSsl = true,
            SslConfig = new SslConfig
            {
                ServerName = serverName ?? host
            }
        };
    }
}

/// <summary>
/// SSL配置
/// </summary>
public class SslConfig
{
    /// <summary>
    /// SSL服务器名称
    /// </summary>
    public string? ServerName { get; set; }

    /// <summary>
    /// 客户端证书
    /// </summary>
    public X509Certificate2? ClientCertificate { get; set; }

    /// <summary>
    /// 是否检查证书撤销
    /// </summary>
    public bool CheckCertificateRevocation { get; set; } = true;

    /// <summary>
    /// 远程证书验证回调
    /// </summary>
    public RemoteCertificateValidationCallback? RemoteCertificateValidationCallback { get; set; }

    /// <summary>
    /// 本地证书选择回调
    /// </summary>
    public LocalCertificateSelectionCallback? LocalCertificateSelectionCallback { get; set; }

    /// <summary>
    /// SSL握手超时时间（秒）
    /// </summary>
    public int HandshakeTimeoutSeconds { get; set; } = TcpClientConstants.Security.SslHandshakeTimeoutSeconds;

    /// <summary>
    /// SSL/TLS协议版本
    /// <para>默认值：SslProtocols.None - 让系统自动选择最安全的协议版本</para>
    /// <para>推荐设置：保持默认值以获得最佳安全性</para>
    /// <para>可选值：Tls12, Tls13, 或其组合</para>
    /// </summary>
    public SslProtocols SslProtocols { get; set; } = SslProtocols.None;

    /// <summary>
    /// 验证配置
    /// </summary>
    /// <returns>验证结果</returns>
    public ValidationResult Validate()
    {
        var errors = new List<string>();

        if (HandshakeTimeoutSeconds <= 0)
        {
            errors.Add("HandshakeTimeoutSeconds必须大于0");
        }

        return new ValidationResult(errors.Count == 0, errors);
    }
}

/// <summary>
/// 连接池配置
/// </summary>
public class ConnectionPoolConfig
{
    /// <summary>
    /// 是否启用连接池
    /// </summary>
    public bool Enabled { get; set; } = false;

    /// <summary>
    /// 连接池大小
    /// </summary>
    public int PoolSize { get; set; } = TcpClientConstants.Defaults.ConnectionPoolSize;

    /// <summary>
    /// 连接空闲超时时间（秒）
    /// </summary>
    public int IdleTimeoutSeconds { get; set; } = 300;

    /// <summary>
    /// 最大连接生存时间（秒）
    /// </summary>
    public int MaxLifetimeSeconds { get; set; } = 3600;

    /// <summary>
    /// 验证配置
    /// </summary>
    /// <returns>验证结果</returns>
    public ValidationResult Validate()
    {
        var errors = new List<string>();

        if (PoolSize <= 0)
        {
            errors.Add("PoolSize必须大于0");
        }

        if (IdleTimeoutSeconds <= 0)
        {
            errors.Add("IdleTimeoutSeconds必须大于0");
        }

        if (MaxLifetimeSeconds <= 0)
        {
            errors.Add("MaxLifetimeSeconds必须大于0");
        }

        return new ValidationResult(errors.Count == 0, errors);
    }
}

/// <summary>
/// 验证结果
/// </summary>
public class ValidationResult
{
    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; }

    /// <summary>
    /// 错误列表
    /// </summary>
    public IReadOnlyList<string> Errors { get; }

    /// <summary>
    /// 初始化验证结果
    /// </summary>
    /// <param name="isValid">是否有效</param>
    /// <param name="errors">错误列表</param>
    public ValidationResult(bool isValid, IEnumerable<string> errors)
    {
        IsValid = isValid;
        Errors = errors.ToList().AsReadOnly();
    }
}
