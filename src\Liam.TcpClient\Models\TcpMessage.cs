using System.Buffers;
using System.Text;
using Microsoft.Extensions.Logging;
using Liam.TcpClient.Constants;
using Liam.TcpClient.Security;

namespace Liam.TcpClient.Models;

/// <summary>
/// TCP消息（与TcpServer兼容）
/// </summary>
public class TcpMessage
{
    /// <summary>
    /// 消息唯一标识符
    /// </summary>
    public string Id { get; }

    /// <summary>
    /// 消息类型
    /// </summary>
    public byte MessageType { get; set; }

    /// <summary>
    /// 消息数据
    /// </summary>
    public byte[] Data { get; set; }

    /// <summary>
    /// 消息长度
    /// </summary>
    public int Length => Data?.Length ?? 0;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; }

    /// <summary>
    /// 发送时间
    /// </summary>
    public DateTime? SentAt { get; set; }

    /// <summary>
    /// 接收时间
    /// </summary>
    public DateTime? ReceivedAt { get; set; }

    /// <summary>
    /// 消息属性
    /// </summary>
    public Dictionary<string, object> Properties { get; }

    /// <summary>
    /// 是否为心跳消息
    /// </summary>
    public bool IsHeartbeat => MessageType == TcpClientConstants.MessageTypes.HeartbeatRequest ||
                               MessageType == TcpClientConstants.MessageTypes.HeartbeatResponse;

    /// <summary>
    /// 是否为控制消息
    /// </summary>
    public bool IsControlMessage => MessageType != TcpClientConstants.MessageTypes.Data;

    /// <summary>
    /// 初始化TCP消息
    /// </summary>
    /// <param name="messageType">消息类型</param>
    /// <param name="data">消息数据</param>
    public TcpMessage(byte messageType, byte[] data)
    {
        Id = Guid.NewGuid().ToString("N");
        MessageType = messageType;
        Data = data ?? Array.Empty<byte>();
        CreatedAt = DateTime.UtcNow;
        Properties = new Dictionary<string, object>();
    }

    /// <summary>
    /// 初始化TCP消息
    /// </summary>
    /// <param name="messageType">消息类型</param>
    /// <param name="text">文本数据</param>
    /// <param name="encoding">文本编码</param>
    public TcpMessage(byte messageType, string text, Encoding? encoding = null)
        : this(messageType, (encoding ?? Encoding.UTF8).GetBytes(text ?? string.Empty))
    {
    }

    /// <summary>
    /// 获取文本数据
    /// </summary>
    /// <param name="encoding">文本编码</param>
    /// <returns>文本数据</returns>
    public string GetText(Encoding? encoding = null)
    {
        if (Data == null || Data.Length == 0)
        {
            return string.Empty;
        }

        return (encoding ?? Encoding.UTF8).GetString(Data);
    }

    /// <summary>
    /// 设置文本数据
    /// </summary>
    /// <param name="text">文本数据</param>
    /// <param name="encoding">文本编码</param>
    public void SetText(string text, Encoding? encoding = null)
    {
        Data = (encoding ?? Encoding.UTF8).GetBytes(text ?? string.Empty);
    }

    /// <summary>
    /// 获取消息属性
    /// </summary>
    /// <typeparam name="T">属性类型</typeparam>
    /// <param name="key">属性键</param>
    /// <returns>属性值</returns>
    public T? GetProperty<T>(string key)
    {
        ArgumentNullException.ThrowIfNull(key);
        
        if (Properties.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        
        return default;
    }

    /// <summary>
    /// 设置消息属性
    /// </summary>
    /// <param name="key">属性键</param>
    /// <param name="value">属性值</param>
    public void SetProperty(string key, object value)
    {
        ArgumentNullException.ThrowIfNull(key);
        ArgumentNullException.ThrowIfNull(value);
        
        Properties[key] = value;
    }

    /// <summary>
    /// 移除消息属性
    /// </summary>
    /// <param name="key">属性键</param>
    /// <returns>是否成功移除</returns>
    public bool RemoveProperty(string key)
    {
        ArgumentNullException.ThrowIfNull(key);
        return Properties.Remove(key);
    }

    /// <summary>
    /// 序列化消息为字节数组
    /// </summary>
    /// <returns>序列化后的字节数组</returns>
    public byte[] Serialize()
    {
        // 消息格式：[消息类型(1字节)] + [数据长度(4字节)] + [数据]
        var totalLength = 1 + 4 + Length;
        var result = new byte[totalLength];
        var span = result.AsSpan();

        // 使用Span<T>进行高效的内存操作
        span[0] = MessageType;

        // 写入数据长度（4字节）
        if (!BitConverter.TryWriteBytes(span.Slice(1, 4), Length))
        {
            throw new InvalidOperationException("无法写入数据长度");
        }

        // 写入数据
        if (Data != null && Data.Length > 0)
        {
            Data.AsSpan().CopyTo(span.Slice(5));
        }

        return result;
    }

    /// <summary>
    /// 从字节数组反序列化消息
    /// </summary>
    /// <param name="bytes">字节数组</param>
    /// <returns>TCP消息</returns>
    /// <exception cref="ArgumentException">字节数组格式无效</exception>
    public static TcpMessage Deserialize(byte[] bytes)
    {
        ArgumentNullException.ThrowIfNull(bytes);
        return Deserialize(bytes.AsSpan());
    }

    /// <summary>
    /// 从字节跨度反序列化消息（高性能版本）
    /// </summary>
    /// <param name="bytes">字节跨度</param>
    /// <returns>TCP消息</returns>
    /// <exception cref="ArgumentException">字节数组格式无效</exception>
    public static TcpMessage Deserialize(ReadOnlySpan<byte> bytes)
    {
        return DeserializeWithValidation(bytes, null, null);
    }

    /// <summary>
    /// 安全反序列化消息（带输入验证）
    /// </summary>
    /// <param name="bytes">字节跨度</param>
    /// <param name="config">客户端配置</param>
    /// <param name="logger">日志记录器</param>
    /// <returns>TCP消息</returns>
    /// <exception cref="ArgumentException">字节数组格式无效或验证失败</exception>
    public static TcpMessage DeserializeWithValidation(ReadOnlySpan<byte> bytes, TcpClientConfig? config, ILogger? logger)
    {
        if (bytes.Length < 5)
        {
            throw new ArgumentException("字节数组长度不足，至少需要5个字节");
        }

        var messageType = bytes[0];
        var dataLength = BitConverter.ToInt32(bytes.Slice(1, 4));

        // 使用默认配置进行基本验证
        var validationConfig = config ?? new TcpClientConfig();

        // 验证消息头
        var headerValidation = InputValidator.ValidateMessageHeader(messageType, dataLength, validationConfig, logger);
        if (!headerValidation.IsValid)
        {
            var errors = string.Join("; ", headerValidation.Errors);
            throw new ArgumentException($"消息头验证失败: {errors}");
        }

        if (bytes.Length < 5 + dataLength)
        {
            throw new ArgumentException($"字节数组长度不足，期望{5 + dataLength}字节，实际{bytes.Length}字节");
        }

        // 验证消息数据
        if (dataLength > 0)
        {
            var dataSpan = bytes.Slice(5, dataLength);
            var dataValidation = InputValidator.ValidateMessageData(dataSpan, validationConfig, logger);
            if (!dataValidation.IsValid)
            {
                var errors = string.Join("; ", dataValidation.Errors);
                logger?.LogWarning("消息数据验证失败: {Errors}", errors);
                // 对于数据验证失败，记录警告但不阻止反序列化（可能是合法的二进制数据）
            }
        }

        // 使用Span<T>进行高效的内存操作
        byte[] data;
        if (dataLength > 0)
        {
            var dataSpan = bytes.Slice(5, dataLength);

            if (dataLength > 1024) // 对于较大的数据使用ArrayPool
            {
                var rentedBuffer = ArrayPool<byte>.Shared.Rent(dataLength);
                try
                {
                    dataSpan.CopyTo(rentedBuffer.AsSpan());
                    data = new byte[dataLength];
                    rentedBuffer.AsSpan(0, dataLength).CopyTo(data.AsSpan());
                }
                finally
                {
                    ArrayPool<byte>.Shared.Return(rentedBuffer);
                }
            }
            else
            {
                // 对于小数据直接分配，使用Span<T>高效复制
                data = new byte[dataLength];
                dataSpan.CopyTo(data.AsSpan());
            }
        }
        else
        {
            data = Array.Empty<byte>();
        }

        return new TcpMessage(messageType, data);
    }

    /// <summary>
    /// 尝试从字节数组反序列化消息
    /// </summary>
    /// <param name="bytes">字节数组</param>
    /// <param name="message">输出的TCP消息</param>
    /// <returns>是否成功反序列化</returns>
    public static bool TryDeserialize(byte[] bytes, out TcpMessage? message)
    {
        message = null;
        
        try
        {
            message = Deserialize(bytes);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 创建数据消息
    /// </summary>
    /// <param name="data">数据</param>
    /// <returns>数据消息</returns>
    public static TcpMessage CreateDataMessage(byte[] data)
    {
        return new TcpMessage(TcpClientConstants.MessageTypes.Data, data);
    }

    /// <summary>
    /// 创建文本消息
    /// </summary>
    /// <param name="text">文本</param>
    /// <param name="encoding">编码</param>
    /// <returns>文本消息</returns>
    public static TcpMessage CreateTextMessage(string text, Encoding? encoding = null)
    {
        return new TcpMessage(TcpClientConstants.MessageTypes.Data, text, encoding);
    }

    /// <summary>
    /// 创建心跳请求消息
    /// </summary>
    /// <returns>心跳请求消息</returns>
    public static TcpMessage CreateHeartbeatRequest()
    {
        return new TcpMessage(TcpClientConstants.MessageTypes.HeartbeatRequest, Array.Empty<byte>());
    }

    /// <summary>
    /// 创建心跳响应消息
    /// </summary>
    /// <returns>心跳响应消息</returns>
    public static TcpMessage CreateHeartbeatResponse()
    {
        return new TcpMessage(TcpClientConstants.MessageTypes.HeartbeatResponse, Array.Empty<byte>());
    }

    /// <summary>
    /// 创建连接确认消息
    /// </summary>
    /// <returns>连接确认消息</returns>
    public static TcpMessage CreateConnectionAck()
    {
        return new TcpMessage(TcpClientConstants.MessageTypes.ConnectionAck, Array.Empty<byte>());
    }

    /// <summary>
    /// 创建断开连接消息
    /// </summary>
    /// <param name="reason">断开原因</param>
    /// <returns>断开连接消息</returns>
    public static TcpMessage CreateDisconnectMessage(string? reason = null)
    {
        var data = string.IsNullOrEmpty(reason) ? Array.Empty<byte>() : Encoding.UTF8.GetBytes(reason);
        return new TcpMessage(TcpClientConstants.MessageTypes.Disconnect, data);
    }

    /// <summary>
    /// 创建错误消息
    /// </summary>
    /// <param name="error">错误信息</param>
    /// <returns>错误消息</returns>
    public static TcpMessage CreateErrorMessage(string error)
    {
        return new TcpMessage(TcpClientConstants.MessageTypes.Error, error, Encoding.UTF8);
    }

    /// <summary>
    /// 重写ToString方法
    /// </summary>
    /// <returns>字符串表示</returns>
    public override string ToString()
    {
        var typeStr = MessageType switch
        {
            TcpClientConstants.MessageTypes.Data => "Data",
            TcpClientConstants.MessageTypes.HeartbeatRequest => "HeartbeatReq",
            TcpClientConstants.MessageTypes.HeartbeatResponse => "HeartbeatResp",
            TcpClientConstants.MessageTypes.ConnectionAck => "ConnAck",
            TcpClientConstants.MessageTypes.Disconnect => "Disconnect",
            TcpClientConstants.MessageTypes.Error => "Error",
            _ => $"Unknown({MessageType:X2})"
        };

        return $"TcpMessage[{Id[..8]}] Type:{typeStr} Length:{Length} Created:{CreatedAt:HH:mm:ss.fff}";
    }
}
